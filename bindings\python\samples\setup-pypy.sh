#!/bin/bash
# Setup PyPy for much faster Python execution
# PyPy is 2-5x faster than regular Python with zero code changes!

set -e

echo "🐍⚡ Setting up PyPy for much faster Python execution..."

# Check if PyPy is already installed
if command -v pypy3 &> /dev/null; then
    echo "✅ PyPy3 is already installed!"
    pypy3 --version
else
    echo "📦 Installing PyPy3..."
    sudo apt-get update
    sudo apt-get install -y pypy3 pypy3-dev pypy3-pip
fi

# Install required packages for PyPy
echo "📦 Installing Python packages for PyPy..."
pypy3 -m pip install --upgrade pip
pypy3 -m pip install requests

# Check if rgbmatrix is available for PyPy
echo "🔍 Checking rgbmatrix availability for PyPy..."
if pypy3 -c "import rgbmatrix" 2>/dev/null; then
    echo "✅ rgbmatrix is available for PyPy!"
else
    echo "⚠️  rgbmatrix not available for PyPy, will use regular Python"
    echo "This is normal - the LED matrix library may not be compiled for PyPy"
fi

# Create a performance comparison script
cat > performance-test.py << 'EOF'
#!/usr/bin/env python3
import time
import sys

def performance_test():
    """Simple performance test to show PyPy vs CPython speed"""
    print(f"Running performance test with {sys.implementation.name}...")
    
    start_time = time.time()
    
    # CPU-intensive task
    total = 0
    for i in range(1000000):
        total += i * i
    
    end_time = time.time()
    elapsed = end_time - start_time
    
    print(f"Completed in {elapsed:.3f} seconds")
    print(f"Result: {total}")
    return elapsed

if __name__ == "__main__":
    performance_test()
EOF

chmod +x performance-test.py

echo ""
echo "✅ PyPy setup complete!"
echo ""
echo "🧪 Performance comparison:"
echo "  # Test with regular Python:"
echo "  python3 performance-test.py"
echo ""
echo "  # Test with PyPy (should be much faster):"
echo "  pypy3 performance-test.py"
echo ""
echo "🚀 To run your LED display with PyPy (if compatible):"
echo "  pypy3 showremotetext_3part_3.py [options]"
echo ""
echo "📊 Expected performance improvements:"
echo "  - 2-5x faster execution"
echo "  - Same exact code"
echo "  - Zero modifications needed"
echo "  - Better memory efficiency"
echo ""
echo "⚠️  Note: If rgbmatrix doesn't work with PyPy, use the Cython approach instead."

# Test PyPy performance
echo ""
echo "🧪 Running quick performance comparison..."
echo "Regular Python:"
python3 performance-test.py

if command -v pypy3 &> /dev/null; then
    echo ""
    echo "PyPy:"
    pypy3 performance-test.py
fi

# Cleanup
rm -f performance-test.py
