#!/usr/bin/env python3
from samplebase import SampleBase
from rgbmatrix import graphics
import time
from PIL import Image, ImageDraw, ImageFont


class PixelPerfectScroll(SampleBase):
    def __init__(self, *args, **kwargs):
        super(PixelPerfectScroll, self).__init__(*args, **kwargs)
        self.parser.add_argument("-t", "--text", help="Middle scrolling text", default="Pixel perfect scrolling text")

    def run(self):
        canvas = self.matrix.CreateFrameCanvas()

        # Font for left/right static labels
        matrix_font = graphics.Font()
        matrix_font.LoadFont("../../../fonts/7x13.bdf")

        # Thin, crisp TTF font for scrolling (adjust path as needed)
        pil_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf", 10)

        # Colors
        color_left = graphics.Color(255, 255, 0)
        color_right = graphics.Color(0, 255, 255)
        scroll_color = (0, 255, 0)  # RGB

        # Scroll area boundaries
        MID_START_X = 40
        MID_END_X = 120
        MID_WIDTH = MID_END_X - MID_START_X
        HEIGHT = canvas.height

        scroll_text = self.args.text + "   "
        scroll_speed = 1  # pixels per frame
        scroll_x = 0

        # Measure scroll text size
        dummy_img = Image.new("1", (1, 1))
        dummy_draw = ImageDraw.Draw(dummy_img)
        text_width, text_height = dummy_draw.textsize(scroll_text, font=pil_font)

        # Create a 1-bit image (black and white, no anti-aliasing)
        scroll_img = Image.new("1", (text_width, HEIGHT), 0)
        draw = ImageDraw.Draw(scroll_img)
        draw.text((0, (HEIGHT - text_height) // 2), scroll_text, font=pil_font, fill=1)

        while True:
            canvas.Clear()

            # Draw fixed left label
            graphics.DrawText(canvas, matrix_font, 0, 14, color_left, "LEFT")

            # Draw fixed right label
            right_text = "RIGHT"
            right_w = graphics.DrawText(canvas, matrix_font, 0, 0, color_right, right_text)
            graphics.DrawText(canvas, matrix_font, canvas.width - right_w, 14, color_right, right_text)

            # Compute current view of scroll image
            x_offset = scroll_x % text_width
            if x_offset + MID_WIDTH <= text_width:
                visible = scroll_img.crop((x_offset, 0, x_offset + MID_WIDTH, HEIGHT))
            else:
                # Wrap around if text is near end
                part1 = scroll_img.crop((x_offset, 0, text_width, HEIGHT))
                part2 = scroll_img.crop((0, 0, MID_WIDTH - (text_width - x_offset), HEIGHT))
                visible = Image.new("1", (MID_WIDTH, HEIGHT))
                visible.paste(part1, (0, 0))
                visible.paste(part2, (part1.width, 0))

            # Draw clipped scroll region
            for x in range(MID_WIDTH):
                for y in range(HEIGHT):
                    pixel = visible.getpixel((x, y))
                    if pixel:  # only draw if pixel is on
                        canvas.SetPixel(MID_START_X + x, y, *scroll_color)

            scroll_x += scroll_speed
            canvas = self.matrix.SwapOnVSync(canvas)
            time.sleep(0.05)


if __name__ == "__main__":
    app = PixelPerfectScroll()
    if not app.process():
        app.print_help()
