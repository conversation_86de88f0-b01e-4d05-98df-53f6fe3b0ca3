#!/usr/bin/env python
from samplebase import SampleBase
from rgbmatrix import graphics
import time


class TwoLineText(SampleBase):
    def __init__(self, *args, **kwargs):
        super(TwoLineText, self).__init__(*args, **kwargs)
        self.parser.add_argument("-l1", "--line1", help="First line of text", default="Line1")
        self.parser.add_argument("-l2", "--line2", help="Second line of text", default="Line2")

    def run(self):
        offscreen_canvas = self.matrix.CreateFrameCanvas()

        font = graphics.Font()
        font.LoadFont("../../../fonts/6x13.bdf")  # Adjust path if needed

        color1 = graphics.Color(255, 0, 0)
        color2 = graphics.Color(0, 255, 0)

        while True:
            offscreen_canvas.Clear()

            # Draw Line 1 and Line 2
            graphics.DrawText(offscreen_canvas, font, 2, 12, color1, self.args.line1)
            graphics.DrawText(offscreen_canvas, font, 2, 28, color2, self.args.line2)

            offscreen_canvas = self.matrix.SwapOnVSync(offscreen_canvas)
            time.sleep(1)


# Main function
if __name__ == "__main__":
    two_line = TwoLineText()
    if not two_line.process():
        two_line.print_help()
