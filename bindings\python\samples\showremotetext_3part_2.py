#!/usr/bin/env python3
import time
import requests
from PIL import Image, ImageDraw, ImageFont
from samplebase import SampleBase
from rgbmatrix import graphics


class SmartScrollDisplay(SampleBase):
    def __init__(self, *args, **kwargs):
        super(SmartScrollDisplay, self).__init__(*args, **kwargs)
        self.parser.add_argument("-u", "--url", required=True, help="JSON URL with left, mid, right for line1 and line2")
        self.parser.add_argument("--color", default="0,255,0", help="Scroll text RGB color, e.g. 255,255,0")
        self.parser.add_argument("--font", default="/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf", help="TTF font path")

    def fetch_remote_data(self):
        try:
            response = requests.get(self.args.url, timeout=3)
            response.raise_for_status()
            data = response.json()
            return data.get("line1", {"left": "", "mid": "", "right": ""}), data.get("line2", {"left": "", "mid": "", "right": ""})
        except Exception as e:
            print(f"[WARN] Fetch failed: {e}")
            return ({"left": "", "mid": "", "right": ""}, {"left": "", "mid": "", "right": ""})

    def render_scroll_image(self, text, font, width, height):
        dummy_img = Image.new("1", (1, 1))
        dummy_draw = ImageDraw.Draw(dummy_img)
        text_width, text_height = dummy_draw.textsize(text, font=font)

        scroll_img = Image.new("1", (text_width, height), 0)
        draw = ImageDraw.Draw(scroll_img)
        draw.text((0, (height - text_height) // 2), text, font=font, fill=1)
        return scroll_img, text_width, text_height

    def draw_scroll_region(self, canvas, scroll_img, scroll_x, x_start, y_start, width, height, color):
        x_offset = scroll_x % scroll_img.width
        if x_offset + width <= scroll_img.width:
            visible = scroll_img.crop((x_offset, 0, x_offset + width, height))
        else:
            part1 = scroll_img.crop((x_offset, 0, scroll_img.width, height))
            part2 = scroll_img.crop((0, 0, width - (scroll_img.width - x_offset), height))
            visible = Image.new("1", (width, height))
            visible.paste(part1, (0, 0))
            visible.paste(part2, (part1.width, 0))

        for x in range(width):
            for y in range(height):
                if visible.getpixel((x, y)):
                    canvas.SetPixel(x_start + x, y_start + y, *color)

    def draw_fixed_mid_text(self, canvas, text, font, x_start, y_start, width, height, color):
        visible = Image.new("1", (width, height), 0)
        draw = ImageDraw.Draw(visible)
        tw, th = draw.textsize(text, font=font)
        xpos = (width - tw) // 2
        draw.text((xpos, (height - th) // 2), text, font=font, fill=1)

        for x in range(width):
            for y in range(height):
                if visible.getpixel((x, y)):
                    canvas.SetPixel(x_start + x, y_start + y, *color)

    def run(self):
        canvas = self.matrix.CreateFrameCanvas()
        HEIGHT = canvas.height
        WIDTH = canvas.width

        # Zones
        MID_X1 = 32
        MID_X2 = 128
        MID_WIDTH = MID_X2 - MID_X1

        # Static font
        static_font = graphics.Font()
        static_font.LoadFont("../../../fonts/6x10.bdf")

        # Scroll font
        try:
            pil_font = ImageFont.truetype(self.args.font, 10)
        except:
            pil_font = ImageFont.load_default()

        scroll_color = tuple(map(int, self.args.color.split(",")))
        scroll_x1, scroll_x2 = 0, 0
        scroll_img1, scroll_img2 = None, None
        text_width1, text_width2 = 1, 1
        text_height = 16

        last_text1 = ""
        last_text2 = ""

        while True:
            line1, line2 = self.fetch_remote_data()
            canvas.Clear()

            # Line 1 Left/Right
            graphics.DrawText(canvas, static_font, 0, 14, graphics.Color(255, 255, 0), line1["left"])
            rtext = line1["right"]
            rw = graphics.DrawText(canvas, static_font, 0, 0, graphics.Color(0, 255, 255), rtext)
            graphics.DrawText(canvas, static_font, WIDTH - rw, 14, graphics.Color(0, 255, 255), rtext)

            # Line 2 Left/Right
            graphics.DrawText(canvas, static_font, 0, 28, graphics.Color(255, 255, 0), line2["left"])
            rtext2 = line2["right"]
            rw2 = graphics.DrawText(canvas, static_font, 0, 0, graphics.Color(0, 255, 255), rtext2)
            graphics.DrawText(canvas, static_font, WIDTH - rw2, 28, graphics.Color(0, 255, 255), rtext2)

            # Re-render scroll image if mid changed
            if line1["mid"] != last_text1:
                scroll_img1, text_width1, text_height = self.render_scroll_image(line1["mid"] + "   ", pil_font, MID_WIDTH, 16)
                last_text1 = line1["mid"]
                scroll_x1 = 0
            if line2["mid"] != last_text2:
                scroll_img2, text_width2, text_height = self.render_scroll_image(line2["mid"] + "   ", pil_font, MID_WIDTH, 16)
                last_text2 = line2["mid"]
                scroll_x2 = 0

            # Line 1 mid
            if text_width1 <= MID_WIDTH:
                self.draw_fixed_mid_text(canvas, line1["mid"], pil_font, MID_X1, 0, MID_WIDTH, 16, scroll_color)
            else:
                self.draw_scroll_region(canvas, scroll_img1, scroll_x1, MID_X1, 0, MID_WIDTH, 16, scroll_color)
                scroll_x1 += 1

            # Line 2 mid
            if text_width2 <= MID_WIDTH:
                self.draw_fixed_mid_text(canvas, line2["mid"], pil_font, MID_X1, 16, MID_WIDTH, 16, scroll_color)
            else:
                self.draw_scroll_region(canvas, scroll_img2, scroll_x2, MID_X1, 16, MID_WIDTH, 16, scroll_color)
                scroll_x2 += 1

            canvas = self.matrix.SwapOnVSync(canvas)
            time.sleep(0.05)


if __name__ == "__main__":
    app = SmartScrollDisplay()
    if not app.process():
        app.print_help()
