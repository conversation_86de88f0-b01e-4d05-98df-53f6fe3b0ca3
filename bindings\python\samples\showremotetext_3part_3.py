#!/usr/bin/env python3
import time
import requests
from samplebase import <PERSON><PERSON>Base
from rgbmatrix import graphics
import threading
import queue


class HighPerformanceScrollDisplay(SampleBase):
    def __init__(self, *args, **kwargs):
        super(HighPerformanceScrollDisplay, self).__init__(*args, **kwargs)
        self.parser.add_argument("-u", "--url", required=True, help="JSON URL with left, mid, right for line1 and line2")
        self.parser.add_argument("--color", default="0,255,0", help="Scroll text RGB color, e.g. 255,255,0")
        self.parser.add_argument("--font", default="../../../fonts/6x10.bdf", help="BDF font path for scrolling text")
        self.parser.add_argument("--scroll-speed", type=int, default=1, help="Scroll speed in pixels per frame (default: 1)")
        self.parser.add_argument("--fps", type=int, default=20, help="Target FPS for smooth scrolling (default: 20)")

        # Performance optimization variables
        self.char_width_cache = {}
        self.data_queue = queue.Queue(maxsize=2)
        self.current_data = None
        self.data_thread = None
        self.running = True

    def fetch_remote_data(self):
        """Fetch data in background thread"""
        try:
            response = requests.get(self.args.url, timeout=3)
            response.raise_for_status()
            data = response.json()
            return data.get("line1", {"left": "", "mid": "", "right": ""}), data.get("line2", {"left": "", "mid": "", "right": ""})
        except Exception as e:
            print(f"[WARN] Fetch failed: {e}")
            return ({"left": "", "mid": "", "right": ""}, {"left": "", "mid": "", "right": ""})

    def data_fetcher_thread(self):
        """Background thread for fetching data - optimized for minimal impact"""
        fetch_interval = 2.0  # Fetch every 2 seconds to reduce load
        last_fetch_time = 0

        while self.running:
            current_time = time.time()

            # Only fetch if enough time has passed
            if current_time - last_fetch_time >= fetch_interval:
                try:
                    data = self.fetch_remote_data()
                    # Only update queue if we have new data and queue isn't full
                    if data and not self.data_queue.full():
                        try:
                            self.data_queue.put_nowait(data)
                            last_fetch_time = current_time
                        except queue.Full:
                            pass  # Skip if queue is full
                except Exception as e:
                    print(f"[DEBUG] Background fetch error: {e}")

            # Short sleep to prevent busy waiting
            time.sleep(0.1)

    def prepare_scroll_data(self, text, font, canvas, width):
        """Prepare scroll data using native graphics library - PIL-free approach"""
        if not text.strip():
            return None, 0, []

        # Check cache first
        cache_key = f"{text}_{width}"
        if cache_key in self.char_width_cache:
            return self.char_width_cache[cache_key]

        # Add padding for smooth scrolling
        scroll_text = text + "   "

        # Pre-calculate character widths using native graphics
        char_widths = []
        for char in scroll_text:
            char_width = graphics.DrawText(canvas, font, 0, 0, graphics.Color(255, 255, 255), char)
            char_widths.append(char_width)

        total_width = sum(char_widths)

        # Cache the result (limit cache size)
        if len(self.char_width_cache) > 20:
            # Clear cache if too large
            self.char_width_cache.clear()

        scroll_data = (scroll_text, total_width, char_widths)
        self.char_width_cache[cache_key] = scroll_data

        return scroll_data

    def draw_native_scroll(self, canvas, scroll_data, scroll_pos, x_start, y_start, width, color, font):
        """Draw scrolling text using native graphics library - PIL-free"""
        if not scroll_data:
            return

        scroll_text, _, char_widths = scroll_data

        # Calculate starting position in pixel space
        pixel_x = -scroll_pos
        x_draw = x_start

        # Draw visible characters only - optimized loop
        i = 0
        while pixel_x < width and i < len(scroll_text):
            char_width = char_widths[i]
            if pixel_x + char_width > 0:  # Character is at least partially visible
                graphics.DrawText(canvas, font, x_draw + pixel_x, y_start, color, scroll_text[i])
            pixel_x += char_width
            i += 1

    def draw_centered_text_native(self, canvas, text, font, x_start, y_start, width, color):
        """Draw centered text using native graphics library - PIL-free"""
        if not text.strip():
            return

        # Measure text width using native graphics
        text_width = graphics.DrawText(canvas, font, 0, 0, graphics.Color(255, 255, 255), text)

        # Calculate centered position
        if text_width <= width:
            x_pos = x_start + (width - text_width) // 2
            graphics.DrawText(canvas, font, x_pos, y_start, color, text)

    def run(self):
        canvas = self.matrix.CreateFrameCanvas()
        WIDTH = canvas.width

        # Zone configuration
        MID_X1 = 32
        MID_X2 = 128
        MID_WIDTH = MID_X2 - MID_X1

        # Font setup - both static and scroll use same BDF font
        static_font = graphics.Font()
        static_font.LoadFont("../../../fonts/6x10.bdf")

        scroll_font = graphics.Font()
        scroll_font.LoadFont(self.args.font)

        # Performance settings
        scroll_color_rgb = tuple(map(int, self.args.color.split(",")))
        scroll_color = graphics.Color(*scroll_color_rgb)
        target_fps = self.args.fps
        frame_time = 1.0 / target_fps
        scroll_speed = self.args.scroll_speed

        # Scroll state
        scroll_pos1 = 0
        scroll_pos2 = 0
        scroll_data1 = None
        scroll_data2 = None

        last_text1 = ""
        last_text2 = ""

        # Pre-calculate static colors to avoid repeated object creation
        color_yellow = graphics.Color(255, 255, 0)
        color_cyan = graphics.Color(0, 255, 255)

        # Cache for right text widths to avoid recalculation
        right_width_cache = {}

        # Start background data fetching
        self.data_thread = threading.Thread(target=self.data_fetcher_thread, daemon=True)
        self.data_thread.start()

        # Initialize with empty data - no blocking fetch
        line1 = line2 = {"left": "", "mid": "", "right": ""}

        try:
            while True:
                loop_start = time.time()

                # Get latest data if available (non-blocking)
                try:
                    while not self.data_queue.empty():
                        self.current_data = self.data_queue.get_nowait()
                        if self.current_data:
                            line1, line2 = self.current_data
                except queue.Empty:
                    pass

                canvas.Clear()

                # Draw static left text (no width calculation needed)
                graphics.DrawText(canvas, static_font, 0, 14, color_yellow, line1["left"])
                graphics.DrawText(canvas, static_font, 0, 28, color_yellow, line2["left"])

                # Draw right text with caching
                rtext1 = line1["right"]
                if rtext1:
                    if rtext1 not in right_width_cache:
                        right_width_cache[rtext1] = graphics.DrawText(canvas, static_font, 0, 0, color_cyan, rtext1)
                        # Limit cache size
                        if len(right_width_cache) > 10:
                            right_width_cache.clear()
                    rw1 = right_width_cache[rtext1]
                    graphics.DrawText(canvas, static_font, WIDTH - rw1, 14, color_cyan, rtext1)

                rtext2 = line2["right"]
                if rtext2:
                    if rtext2 not in right_width_cache:
                        right_width_cache[rtext2] = graphics.DrawText(canvas, static_font, 0, 0, color_cyan, rtext2)
                        if len(right_width_cache) > 10:
                            right_width_cache.clear()
                    rw2 = right_width_cache[rtext2]
                    graphics.DrawText(canvas, static_font, WIDTH - rw2, 28, color_cyan, rtext2)

                # Update scroll data if text changed - do this quickly
                if line1["mid"] != last_text1:
                    if line1["mid"].strip():  # Only process non-empty text
                        scroll_data1 = self.prepare_scroll_data(line1["mid"], scroll_font, canvas, MID_WIDTH)
                    else:
                        scroll_data1 = None
                    last_text1 = line1["mid"]
                    scroll_pos1 = 0

                if line2["mid"] != last_text2:
                    if line2["mid"].strip():  # Only process non-empty text
                        scroll_data2 = self.prepare_scroll_data(line2["mid"], scroll_font, canvas, MID_WIDTH)
                    else:
                        scroll_data2 = None
                    last_text2 = line2["mid"]
                    scroll_pos2 = 0

                # Draw middle sections - optimized rendering
                if scroll_data1:
                    _, total_width1, _ = scroll_data1
                    if total_width1 <= MID_WIDTH:
                        # Center the text if it fits
                        self.draw_centered_text_native(canvas, line1["mid"], scroll_font, MID_X1, 14, MID_WIDTH, scroll_color)
                    else:
                        # Scroll the text if it's too long
                        self.draw_native_scroll(canvas, scroll_data1, scroll_pos1, MID_X1, 14, MID_WIDTH, scroll_color, scroll_font)
                        scroll_pos1 = (scroll_pos1 + scroll_speed) % total_width1
                elif line1["mid"].strip():
                    # Fallback for short text without scroll data
                    self.draw_centered_text_native(canvas, line1["mid"], scroll_font, MID_X1, 14, MID_WIDTH, scroll_color)

                if scroll_data2:
                    _, total_width2, _ = scroll_data2
                    if total_width2 <= MID_WIDTH:
                        # Center the text if it fits
                        self.draw_centered_text_native(canvas, line2["mid"], scroll_font, MID_X1, 28, MID_WIDTH, scroll_color)
                    else:
                        # Scroll the text if it's too long
                        self.draw_native_scroll(canvas, scroll_data2, scroll_pos2, MID_X1, 28, MID_WIDTH, scroll_color, scroll_font)
                        scroll_pos2 = (scroll_pos2 + scroll_speed) % total_width2
                elif line2["mid"].strip():
                    # Fallback for short text without scroll data
                    self.draw_centered_text_native(canvas, line2["mid"], scroll_font, MID_X1, 28, MID_WIDTH, scroll_color)

                canvas = self.matrix.SwapOnVSync(canvas)

                # Improved frame rate control - more precise timing
                elapsed = time.time() - loop_start
                sleep_time = frame_time - elapsed
                if sleep_time > 0:
                    time.sleep(sleep_time)

        except KeyboardInterrupt:
            self.running = False
            if self.data_thread:
                self.data_thread.join(timeout=1.0)


if __name__ == "__main__":
    app = HighPerformanceScrollDisplay()
    if not app.process():
        app.print_help()
