#!/usr/bin/env python3
from samplebase import SampleBase
from rgbmatrix import graphics
import time
from PIL import Image, ImageDraw, ImageFont


class PixelClippedScroll(SampleBase):
    def __init__(self, *args, **kwargs):
        super(PixelClippedScroll, self).__init__(*args, **kwargs)
        self.parser.add_argument("-t", "--text", help="Middle scrolling text", default="Scrolling in middle only")

    def run(self):
        canvas = self.matrix.CreateFrameCanvas()

        # RGBMatrix font (for fixed left/right text)
        matrix_font = graphics.Font()
        matrix_font.LoadFont("../../../fonts/7x13.bdf")

        # PIL font for smooth clipped scrolling
        pil_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf", 12)

        color_left = graphics.Color(255, 255, 0)
        color_right = graphics.Color(0, 255, 255)

        # Scrolling region
        MID_START_X = 40
        MID_END_X = 120
        MID_WIDTH = MID_END_X - MID_START_X
        HEIGHT = canvas.height

        scroll_text = self.args.text + "   "
        scroll_speed = 1  # pixels per frame
        scroll_x = 0

        # Pre-render text to image
        dummy_img = Image.new("RGB", (1, 1))
        dummy_draw = ImageDraw.Draw(dummy_img)
        text_width, text_height = dummy_draw.textsize(scroll_text, font=pil_font)

        scroll_img = Image.new("RGB", (text_width, HEIGHT), "black")
        draw = ImageDraw.Draw(scroll_img)
        draw.text((0, (HEIGHT - text_height) // 2), scroll_text, font=pil_font, fill=(0, 255, 0))

        while True:
            canvas.Clear()

            # Fixed left and right labels
            graphics.DrawText(canvas, matrix_font, 0, 14, color_left, "LEFT")
            right_text = "RIGHT"
            right_w = graphics.DrawText(canvas, matrix_font, 0, 0, color_right, right_text)
            graphics.DrawText(canvas, matrix_font, canvas.width - right_w, 14, color_right, right_text)

            # Crop the visible scroll region from scroll image
            x_offset = scroll_x % text_width
            visible = scroll_img.crop((x_offset, 0, x_offset + MID_WIDTH, HEIGHT))

            # If crop goes out of bounds, stitch second part
            if x_offset + MID_WIDTH > text_width:
                part1 = scroll_img.crop((x_offset, 0, text_width, HEIGHT))
                part2 = scroll_img.crop((0, 0, MID_WIDTH - (text_width - x_offset), HEIGHT))
                visible = Image.new("RGB", (MID_WIDTH, HEIGHT))
                visible.paste(part1, (0, 0))
                visible.paste(part2, (part1.width, 0))

            # Blit only into scrolling region
            for x in range(MID_WIDTH):
                for y in range(HEIGHT):
                    r, g, b = visible.getpixel((x, y))
                    canvas.SetPixel(MID_START_X + x, y, r, g, b)

            scroll_x += scroll_speed
            canvas = self.matrix.SwapOnVSync(canvas)
            time.sleep(0.05)


if __name__ == "__main__":
    app = PixelClippedScroll()
    if not app.process():
        app.print_help()
