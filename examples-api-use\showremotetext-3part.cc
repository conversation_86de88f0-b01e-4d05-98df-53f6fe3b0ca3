// -*- mode: c++; c-basic-offset: 2; indent-tabs-mode: nil; -*-
// High-performance 3-part remote text display with scrolling
// Optimized for Raspberry Pi Zero - much faster than Python version
//
// This code is public domain
// (but note, that the led-matrix library this depends on is GPL v2)

#include "led-matrix.h"
#include "graphics.h"

#include <unistd.h>
#include <math.h>
#include <stdio.h>
#include <signal.h>
#include <string.h>
#include <stdlib.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include <pthread.h>
#include <time.h>
#include <cstring>

using namespace rgb_matrix;

volatile bool interrupt_received = false;
static void InterruptHandler(int signo) {
  interrupt_received = true;
}

// Structure to hold display data
struct DisplayData {
  char line1_left[256];
  char line1_mid[256];
  char line1_right[256];
  char line2_left[256];
  char line2_mid[256];
  char line2_right[256];
  bool updated;
  pthread_mutex_t mutex;
};

// Structure for HTTP response
struct HTTPResponse {
  char *memory;
  size_t size;
};

// Callback for curl to write response data
static size_t WriteMemoryCallback(void *contents, size_t size, size_t nmemb, struct HTTPResponse *response) {
  size_t realsize = size * nmemb;
  char *ptr = (char*)realloc(response->memory, response->size + realsize + 1);
  if (!ptr) {
    printf("Not enough memory (realloc returned NULL)\n");
    return 0;
  }
  
  response->memory = ptr;
  memcpy(&(response->memory[response->size]), contents, realsize);
  response->size += realsize;
  response->memory[response->size] = 0;
  
  return realsize;
}

// Parse JSON and update display data
bool ParseJSONData(const char* json_str, DisplayData* data) {
  json_object *root = json_tokener_parse(json_str);
  if (!root) return false;
  
  pthread_mutex_lock(&data->mutex);
  
  // Parse line1
  json_object *line1;
  if (json_object_object_get_ex(root, "line1", &line1)) {
    json_object *left, *mid, *right;
    if (json_object_object_get_ex(line1, "left", &left)) {
      strncpy(data->line1_left, json_object_get_string(left), 255);
    }
    if (json_object_object_get_ex(line1, "mid", &mid)) {
      strncpy(data->line1_mid, json_object_get_string(mid), 255);
    }
    if (json_object_object_get_ex(line1, "right", &right)) {
      strncpy(data->line1_right, json_object_get_string(right), 255);
    }
  }
  
  // Parse line2
  json_object *line2;
  if (json_object_object_get_ex(root, "line2", &line2)) {
    json_object *left, *mid, *right;
    if (json_object_object_get_ex(line2, "left", &left)) {
      strncpy(data->line2_left, json_object_get_string(left), 255);
    }
    if (json_object_object_get_ex(line2, "mid", &mid)) {
      strncpy(data->line2_mid, json_object_get_string(mid), 255);
    }
    if (json_object_object_get_ex(line2, "right", &right)) {
      strncpy(data->line2_right, json_object_get_string(right), 255);
    }
  }
  
  data->updated = true;
  pthread_mutex_unlock(&data->mutex);
  
  json_object_put(root);
  return true;
}

// Fetch data from URL
bool FetchRemoteData(const char* url, DisplayData* data) {
  CURL *curl;
  CURLcode res;
  struct HTTPResponse response;
  response.memory = nullptr;
  response.size = 0;
  
  curl = curl_easy_init();
  if (!curl) return false;
  
  curl_easy_setopt(curl, CURLOPT_URL, url);
  curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
  curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&response);
  curl_easy_setopt(curl, CURLOPT_TIMEOUT, 2L);  // 2 second timeout
  curl_easy_setopt(curl, CURLOPT_USERAGENT, "rpi-led-matrix/1.0");
  
  res = curl_easy_perform(curl);
  curl_easy_cleanup(curl);
  
  bool success = false;
  if (res == CURLE_OK && response.memory) {
    success = ParseJSONData(response.memory, data);
  }
  
  if (response.memory) {
    free(response.memory);
  }
  
  return success;
}

// Background thread for fetching data
void* DataFetcherThread(void* arg) {
  struct {
    const char* url;
    DisplayData* data;
  } *params = (decltype(params))arg;
  
  while (!interrupt_received) {
    FetchRemoteData(params->url, params->data);
    sleep(2);  // Fetch every 2 seconds
  }
  
  return nullptr;
}

// Calculate text width for centering
int CalculateTextWidth(const Font& font, const char* text) {
  int width = 0;
  for (const char* p = text; *p; ++p) {
    width += font.CharacterWidth(*p);
  }
  return width;
}

// Draw scrolling text in a region
void DrawScrollingText(FrameCanvas* canvas, const Font& font, 
                      const char* text, int& scroll_pos, 
                      int x_start, int y, int region_width, 
                      const Color& color) {
  if (!text || !*text) return;
  
  // Add padding for smooth scrolling
  char padded_text[512];
  snprintf(padded_text, sizeof(padded_text), "%s   ", text);
  
  int total_width = CalculateTextWidth(font, padded_text);
  
  if (total_width <= region_width) {
    // Center the text if it fits
    int x_centered = x_start + (region_width - CalculateTextWidth(font, text)) / 2;
    DrawText(canvas, font, x_centered, y, color, nullptr, text);
  } else {
    // Scroll the text
    int x_pos = x_start - scroll_pos;
    
    // Draw characters that are visible in the region
    const char* p = padded_text;
    while (*p && x_pos < x_start + region_width) {
      int char_width = font.CharacterWidth(*p);
      if (x_pos + char_width > x_start) {
        char single_char[2] = {*p, '\0'};
        DrawText(canvas, font, x_pos, y, color, nullptr, single_char);
      }
      x_pos += char_width;
      ++p;
    }
    
    // Update scroll position
    scroll_pos = (scroll_pos + 1) % total_width;
  }
}

int main(int argc, char *argv[]) {
  RGBMatrix::Options defaults;
  defaults.hardware_mapping = "regular";
  defaults.rows = 32;
  defaults.cols = 160;
  defaults.chain_length = 5;
  defaults.parallel = 1;
  defaults.brightness = 80;
  defaults.pwm_bits = 11;
  defaults.pwm_lsb_nanoseconds = 130;
  defaults.led_rgb_sequence = "RGB";
  defaults.pixel_mapper_config = "";
  defaults.row_address_type = 0;
  defaults.multiplexing = 1;
  defaults.scan_mode = 0;
  defaults.disable_hardware_pulsing = false;
  defaults.show_refresh_rate = false;
  defaults.inverse_colors = false;

  RuntimeOptions runtime_opt;
  runtime_opt.gpio_slowdown = 2;
  runtime_opt.daemon = 0;
  runtime_opt.drop_privileges = 1;
  
  // Parse command line arguments
  const char* url = "http://*************/panel-message-3part.json";
  int scroll_speed = 1;
  
  for (int i = 1; i < argc; ++i) {
    if (strcmp(argv[i], "-u") == 0 || strcmp(argv[i], "--url") == 0) {
      if (i + 1 < argc) url = argv[++i];
    } else if (strcmp(argv[i], "--scroll-speed") == 0) {
      if (i + 1 < argc) scroll_speed = atoi(argv[++i]);
    } else if (strcmp(argv[i], "--led-brightness") == 0) {
      if (i + 1 < argc) defaults.brightness = atoi(argv[++i]);
    } else if (strcmp(argv[i], "--led-chain") == 0) {
      if (i + 1 < argc) defaults.chain_length = atoi(argv[++i]);
    } else if (strcmp(argv[i], "--led-slowdown-gpio") == 0) {
      if (i + 1 < argc) {
        runtime_opt.gpio_slowdown = atoi(argv[++i]);
      }
    } else if (strcmp(argv[i], "--led-row-addr-type") == 0) {
      if (i + 1 < argc) defaults.row_address_type = atoi(argv[++i]);
    } else if (strcmp(argv[i], "--led-multiplexing") == 0) {
      if (i + 1 < argc) defaults.multiplexing = atoi(argv[++i]);
    }
  }
  
  // Initialize curl
  curl_global_init(CURL_GLOBAL_DEFAULT);
  
  // Initialize display data
  DisplayData display_data;
  memset(&display_data, 0, sizeof(display_data));
  display_data.updated = false;
  pthread_mutex_init(&display_data.mutex, nullptr);
  
  // Create matrix
  RGBMatrix *matrix = RGBMatrix::CreateFromOptions(defaults, runtime_opt);
  if (matrix == nullptr) {
    fprintf(stderr, "Failed to create matrix\n");
    return 1;
  }
  
  // Load font
  Font font;
  if (!font.LoadFont("../fonts/6x10.bdf")) {
    fprintf(stderr, "Couldn't load font\n");
    return 1;
  }
  
  // Set up signal handler
  signal(SIGTERM, InterruptHandler);
  signal(SIGINT, InterruptHandler);
  
  // Start background data fetcher
  pthread_t fetch_thread;
  struct {
    const char* url;
    DisplayData* data;
  } fetch_params = {url, &display_data};
  
  pthread_create(&fetch_thread, nullptr, DataFetcherThread, &fetch_params);
  
  // Display loop
  FrameCanvas *offscreen = matrix->CreateFrameCanvas();
  
  // Colors
  Color color_yellow(255, 255, 0);
  Color color_cyan(0, 255, 255);
  Color color_green(0, 255, 0);
  
  // Layout constants
  const int MID_X1 = 32;
  const int MID_X2 = 128;
  const int MID_WIDTH = MID_X2 - MID_X1;
  
  // Scroll state
  int scroll_pos1 = 0;
  int scroll_pos2 = 0;
  
  // Local copies of text data
  char local_line1_left[256] = "";
  char local_line1_mid[256] = "";
  char local_line1_right[256] = "";
  char local_line2_left[256] = "";
  char local_line2_mid[256] = "";
  char local_line2_right[256] = "";
  
  printf("Starting display loop. URL: %s\n", url);
  printf("Press CTRL-C to stop.\n");
  
  while (!interrupt_received) {
    // Copy data from shared structure if updated
    if (display_data.updated) {
      pthread_mutex_lock(&display_data.mutex);
      strcpy(local_line1_left, display_data.line1_left);
      strcpy(local_line1_mid, display_data.line1_mid);
      strcpy(local_line1_right, display_data.line1_right);
      strcpy(local_line2_left, display_data.line2_left);
      strcpy(local_line2_mid, display_data.line2_mid);
      strcpy(local_line2_right, display_data.line2_right);
      display_data.updated = false;
      pthread_mutex_unlock(&display_data.mutex);
    }
    
    offscreen->Clear();
    
    // Draw static left text
    DrawText(offscreen, font, 0, 14, color_yellow, nullptr, local_line1_left);
    DrawText(offscreen, font, 0, 28, color_yellow, nullptr, local_line2_left);
    
    // Draw right text (right-aligned)
    if (strlen(local_line1_right) > 0) {
      int right_width1 = CalculateTextWidth(font, local_line1_right);
      DrawText(offscreen, font, matrix->width() - right_width1, 14, color_cyan, nullptr, local_line1_right);
    }
    
    if (strlen(local_line2_right) > 0) {
      int right_width2 = CalculateTextWidth(font, local_line2_right);
      DrawText(offscreen, font, matrix->width() - right_width2, 28, color_cyan, nullptr, local_line2_right);
    }
    
    // Draw scrolling middle text
    DrawScrollingText(offscreen, font, local_line1_mid, scroll_pos1, MID_X1, 14, MID_WIDTH, color_green);
    DrawScrollingText(offscreen, font, local_line2_mid, scroll_pos2, MID_X1, 28, MID_WIDTH, color_green);
    
    // Swap buffers
    offscreen = matrix->SwapOnVSync(offscreen);
    
    // Control frame rate (50 FPS = 20ms)
    usleep(20000);
  }
  
  // Cleanup
  pthread_cancel(fetch_thread);
  pthread_join(fetch_thread, nullptr);
  pthread_mutex_destroy(&display_data.mutex);
  curl_global_cleanup();
  delete matrix;
  
  printf("\nReceived CTRL-C. Exiting.\n");
  return 0;
}
