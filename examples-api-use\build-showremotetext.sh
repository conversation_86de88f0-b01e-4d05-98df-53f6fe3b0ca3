#!/bin/bash
# Build script for showremotetext-3part C++ version
# High-performance alternative to Python version

set -e

echo "Building showremotetext-3part C++ version..."

# Check if we're in the right directory
if [ ! -f "showremotetext-3part.cc" ]; then
    echo "Error: showremotetext-3part.cc not found!"
    echo "Please run this script from the examples-api-use directory"
    exit 1
fi

# Install dependencies if needed
echo "Checking dependencies..."
if ! pkg-config --exists libcurl; then
    echo "Installing libcurl development package..."
    sudo apt-get update
    sudo apt-get install -y libcurl4-openssl-dev
fi

if ! pkg-config --exists json-c; then
    echo "Installing json-c development package..."
    sudo apt-get install -y libjson-c-dev
fi

# Build the main library first
echo "Building RGB matrix library..."
make -C .. -j$(nproc)

# Build our program
echo "Building showremotetext-3part..."
make -f Makefile.showremotetext clean
make -f Makefile.showremotetext -j$(nproc)

if [ -f "showremotetext-3part" ]; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    echo "Usage examples:"
    echo "  # Basic usage with defaults:"
    echo "  sudo ./showremotetext-3part"
    echo ""
    echo "  # With custom URL and settings:"
    echo "  sudo ./showremotetext-3part -u http://your-server/data.json --led-brightness=60"
    echo ""
    echo "  # Full parameter example for RPi Zero:"
    echo "  sudo ./showremotetext-3part \\"
    echo "    --led-row-addr-type=0 \\"
    echo "    --led-multiplexing=1 \\"
    echo "    --led-slowdown-gpio=2 \\"
    echo "    --led-chain=5 \\"
    echo "    --led-brightness=80 \\"
    echo "    -u http://*************/panel-message-3part.json"
    echo ""
    echo "Performance benefits over Python version:"
    echo "  - 🚀 Much faster rendering (native C++)"
    echo "  - 💾 Lower memory usage"
    echo "  - ⚡ Better frame rates"
    echo "  - 🔧 Optimized for Raspberry Pi Zero"
    echo ""
else
    echo "❌ Build failed!"
    exit 1
fi
