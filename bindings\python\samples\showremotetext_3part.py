#!/usr/bin/env python3
from samplebase import SampleBase
from rgbmatrix import graphics
import time
import requests


class RemoteTextDisplay(SampleBase):
    def __init__(self, *args, **kwargs):
        super(RemoteTextDisplay, self).__init__(*args, **kwargs)
        self.parser.add_argument("-u", "--url", help="Remote JSON endpoint", required=True)
        self.parser.add_argument("-i", "--interval", type=int, default=5, help="Refresh interval in seconds")

    def fetch_remote_data(self, url):
        try:
            response = requests.get(url, timeout=3)
            response.raise_for_status()
            data = response.json()
            return (
                data.get("line1", {"left": "", "mid": "", "right": ""}),
                data.get("line2", {"left": "", "mid": "", "right": ""})
            )
        except Exception as e:
            print(f"[WARN] Failed to fetch remote data: {e}")
            return {"left": "", "mid": "", "right": ""}, {"left": "", "mid": "", "right": ""}

    def run(self):
        offscreen_canvas = self.matrix.CreateFrameCanvas()
        font = graphics.Font()
        font.LoadFont("../../../fonts/7x14.bdf")  # Adjust path if needed

        color_left = graphics.Color(255, 255, 0)
        color_mid = graphics.Color(0, 255, 0)
        color_right = graphics.Color(0, 255, 255)

        display_width = self.matrix.width
        fixed_left_width = 40   # Change as needed
        fixed_right_width = 40  # Change as needed
        mid_area_width = display_width - fixed_left_width - fixed_right_width

        scroll_pos1 = 0
        scroll_pos2 = 0

        last_data1 = {"left": "", "mid": "", "right": ""}
        last_data2 = {"left": "", "mid": "", "right": ""}

        while True:
            line1, line2 = self.fetch_remote_data(self.args.url)

            # Reset scroll if data changes
            if line1 != last_data1:
                scroll_pos1 = 0
                last_data1 = line1.copy()
            if line2 != last_data2:
                scroll_pos2 = 0
                last_data2 = line2.copy()

            offscreen_canvas.Clear()

            # --- Line 1 ---
            graphics.DrawText(offscreen_canvas, font, 0, 12, color_left, line1["left"])
            graphics.DrawText(offscreen_canvas, font, display_width - fixed_right_width, 12, color_right, line1["right"])

            text_width1 = graphics.DrawText(offscreen_canvas, font, 0, 0, color_mid, line1["mid"])
            if text_width1 > mid_area_width:
                scroll_text1 = line1["mid"] + "   "  # Padding
                offset1 = scroll_pos1 % (graphics.DrawText(offscreen_canvas, font, 0, 0, color_mid, scroll_text1))
                graphics.DrawText(offscreen_canvas, font, fixed_left_width - offset1, 12, color_mid, scroll_text1)
                scroll_pos1 += 1
            else:
                mid_x1 = fixed_left_width + (mid_area_width - text_width1) // 2
                graphics.DrawText(offscreen_canvas, font, mid_x1, 12, color_mid, line1["mid"])

            # --- Line 2 ---
            graphics.DrawText(offscreen_canvas, font, 0, 28, color_left, line2["left"])
            graphics.DrawText(offscreen_canvas, font, display_width - fixed_right_width, 28, color_right, line2["right"])

            text_width2 = graphics.DrawText(offscreen_canvas, font, 0, 0, color_mid, line2["mid"])
            if text_width2 > mid_area_width:
                scroll_text2 = line2["mid"] + "   "
                offset2 = scroll_pos2 % (graphics.DrawText(offscreen_canvas, font, 0, 0, color_mid, scroll_text2))
                graphics.DrawText(offscreen_canvas, font, fixed_left_width - offset2, 28, color_mid, scroll_text2)
                scroll_pos2 += 1
            else:
                mid_x2 = fixed_left_width + (mid_area_width - text_width2) // 2
                graphics.DrawText(offscreen_canvas, font, mid_x2, 28, color_mid, line2["mid"])

            offscreen_canvas = self.matrix.SwapOnVSync(offscreen_canvas)
            time.sleep(0.05)


# --- Entry Point ---
if __name__ == "__main__":
    display = RemoteTextDisplay()
    if not display.process():
        display.print_help()
