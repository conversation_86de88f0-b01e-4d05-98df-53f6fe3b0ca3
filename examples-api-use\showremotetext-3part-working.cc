// -*- mode: c++; c-basic-offset: 2; indent-tabs-mode: nil; -*-
// 3-part remote text display with scrolling - C++ version
// Based on working showremotetext_3part_3.py functionality
// 
// This code is public domain
// (but note, that the led-matrix library this depends on is GPL v2)

#include "led-matrix.h"
#include "graphics.h"

#include <unistd.h>
#include <math.h>
#include <stdio.h>
#include <signal.h>
#include <string.h>
#include <stdlib.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include <pthread.h>
#include <time.h>
#include <vector>
#include <string>
#include <chrono>

using namespace rgb_matrix;

volatile bool interrupt_received = false;
static void InterruptHandler(int signo) {
  interrupt_received = true;
}

// Structure to hold display data (matches Python version)
struct DisplayData {
  char line1_left[256];
  char line1_mid[256];
  char line1_right[256];
  char line2_left[256];
  char line2_mid[256];
  char line2_right[256];
  bool updated;
  pthread_mutex_t mutex;
};

// Structure for HTTP response
struct HTTPResponse {
  char *memory;
  size_t size;
};

// Scroll data structure (matches Python scroll_data tuple)
struct ScrollData {
  std::string text;
  int total_width;
  std::vector<int> char_widths;
  bool valid;
  
  ScrollData() : total_width(0), valid(false) {}
};

// Callback for curl to write response data
static size_t WriteMemoryCallback(void *contents, size_t size, size_t nmemb, struct HTTPResponse *response) {
  size_t realsize = size * nmemb;
  char *ptr = (char*)realloc(response->memory, response->size + realsize + 1);
  if (!ptr) {
    printf("Not enough memory (realloc returned NULL)\n");
    return 0;
  }
  
  response->memory = ptr;
  memcpy(&(response->memory[response->size]), contents, realsize);
  response->size += realsize;
  response->memory[response->size] = 0;
  
  return realsize;
}

// Parse JSON and update display data (matches Python fetch_remote_data)
bool ParseJSONData(const char* json_str, DisplayData* data) {
  json_object *root = json_tokener_parse(json_str);
  if (!root) return false;
  
  pthread_mutex_lock(&data->mutex);
  
  // Initialize with empty strings
  strcpy(data->line1_left, "");
  strcpy(data->line1_mid, "");
  strcpy(data->line1_right, "");
  strcpy(data->line2_left, "");
  strcpy(data->line2_mid, "");
  strcpy(data->line2_right, "");
  
  // Parse line1
  json_object *line1;
  if (json_object_object_get_ex(root, "line1", &line1)) {
    json_object *left, *mid, *right;
    if (json_object_object_get_ex(line1, "left", &left)) {
      strncpy(data->line1_left, json_object_get_string(left), 255);
      data->line1_left[255] = '\0';
    }
    if (json_object_object_get_ex(line1, "mid", &mid)) {
      strncpy(data->line1_mid, json_object_get_string(mid), 255);
      data->line1_mid[255] = '\0';
    }
    if (json_object_object_get_ex(line1, "right", &right)) {
      strncpy(data->line1_right, json_object_get_string(right), 255);
      data->line1_right[255] = '\0';
    }
  }
  
  // Parse line2
  json_object *line2;
  if (json_object_object_get_ex(root, "line2", &line2)) {
    json_object *left, *mid, *right;
    if (json_object_object_get_ex(line2, "left", &left)) {
      strncpy(data->line2_left, json_object_get_string(left), 255);
      data->line2_left[255] = '\0';
    }
    if (json_object_object_get_ex(line2, "mid", &mid)) {
      strncpy(data->line2_mid, json_object_get_string(mid), 255);
      data->line2_mid[255] = '\0';
    }
    if (json_object_object_get_ex(line2, "right", &right)) {
      strncpy(data->line2_right, json_object_get_string(right), 255);
      data->line2_right[255] = '\0';
    }
  }
  
  data->updated = true;
  pthread_mutex_unlock(&data->mutex);
  
  json_object_put(root);
  return true;
}

// Fetch data from URL (matches Python fetch_remote_data)
bool FetchRemoteData(const char* url, DisplayData* data) {
  CURL *curl;
  CURLcode res;
  struct HTTPResponse response;
  response.memory = nullptr;
  response.size = 0;
  
  curl = curl_easy_init();
  if (!curl) return false;
  
  curl_easy_setopt(curl, CURLOPT_URL, url);
  curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
  curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&response);
  curl_easy_setopt(curl, CURLOPT_TIMEOUT, 2L);  // 2 second timeout (matches Python)
  curl_easy_setopt(curl, CURLOPT_USERAGENT, "rpi-led-matrix/1.0");
  
  res = curl_easy_perform(curl);
  curl_easy_cleanup(curl);
  
  bool success = false;
  if (res == CURLE_OK && response.memory) {
    success = ParseJSONData(response.memory, data);
  }
  
  if (response.memory) {
    free(response.memory);
  }
  
  return success;
}

// Background thread for fetching data (matches Python data_fetcher_thread)
void* DataFetcherThread(void* arg) {
  struct {
    const char* url;
    DisplayData* data;
  } *params = (decltype(params))arg;
  
  time_t last_fetch_time = 0;
  const int fetch_interval = 2; // 2 seconds (matches Python)
  
  while (!interrupt_received) {
    time_t current_time = time(nullptr);
    
    // Only fetch if enough time has passed (matches Python logic)
    if (current_time - last_fetch_time >= fetch_interval) {
      if (FetchRemoteData(params->url, params->data)) {
        last_fetch_time = current_time;
      }
    }
    
    usleep(100000); // 0.1 second sleep (matches Python)
  }
  
  return nullptr;
}

// Prepare scroll data (matches Python prepare_scroll_data)
ScrollData PrepareScrollData(const std::string& text, const Font& font, FrameCanvas* canvas, int width) {
  ScrollData scroll_data;
  
  if (text.empty()) {
    return scroll_data;
  }
  
  // Add padding for smooth scrolling (matches Python)
  scroll_data.text = text + "   ";
  
  // Pre-calculate character widths (matches Python logic)
  scroll_data.char_widths.clear();
  scroll_data.total_width = 0;
  
  for (char c : scroll_data.text) {
    int char_width = font.CharacterWidth(c);
    scroll_data.char_widths.push_back(char_width);
    scroll_data.total_width += char_width;
  }
  
  scroll_data.valid = true;
  return scroll_data;
}

// Draw scrolling text (matches Python draw_native_scroll)
void DrawScrollingText(FrameCanvas* canvas, const Font& font, const ScrollData& scroll_data, 
                      int scroll_pos, int x_start, int y, int width, const Color& color) {
  if (!scroll_data.valid) return;
  
  // Calculate starting position in pixel space (matches Python)
  int pixel_x = -scroll_pos;
  int x_draw = x_start;
  
  // Draw visible characters only (matches Python logic)
  for (size_t i = 0; i < scroll_data.text.length() && pixel_x < width; ++i) {
    int char_width = scroll_data.char_widths[i];
    if (pixel_x + char_width > 0) { // Character is at least partially visible
      char single_char[2] = {scroll_data.text[i], '\0'};
      DrawText(canvas, font, x_draw + pixel_x, y, color, nullptr, single_char);
    }
    pixel_x += char_width;
  }
}

// Draw centered text (matches Python draw_centered_text_native)
void DrawCenteredText(FrameCanvas* canvas, const Font& font, const std::string& text,
                     int x_start, int y, int width, const Color& color) {
  if (text.empty()) return;
  
  // Calculate text width
  int text_width = 0;
  for (char c : text) {
    text_width += font.CharacterWidth(c);
  }
  
  // Center the text if it fits (matches Python logic)
  if (text_width <= width) {
    int x_pos = x_start + (width - text_width) / 2;
    DrawText(canvas, font, x_pos, y, color, nullptr, text.c_str());
  }
}

static int usage(const char *progname) {
  fprintf(stderr, "usage: %s [options]\n", progname);
  fprintf(stderr, "Options:\n");
  fprintf(stderr, "\t-u <url>     : JSON URL (default: http://*************/panel-message-3part.json)\n");
  fprintf(stderr, "\t-s <speed>   : Scroll speed in pixels per frame (default: 1)\n");
  fprintf(stderr, "\t-f <font>    : BDF font file (default: ../fonts/6x10.bdf)\n");
  fprintf(stderr, "\t-c <r,g,b>   : Scroll text color (default: 0,255,0)\n");
  fprintf(stderr, "\t-fps <fps>   : Target FPS (default: 10)\n");
  fprintf(stderr, "\n");
  rgb_matrix::PrintMatrixFlags(stderr);
  return 1;
}

static bool parseColor(Color *c, const char *str) {
  return sscanf(str, "%hhu,%hhu,%hhu", &c->r, &c->g, &c->b) == 3;
}

int main(int argc, char *argv[]) {
  RGBMatrix::Options matrix_options;
  rgb_matrix::RuntimeOptions runtime_opt;

  // Set defaults (matches Python defaults)
  matrix_options.hardware_mapping = "regular";
  matrix_options.rows = 32;
  matrix_options.cols = 160;
  matrix_options.chain_length = 5;
  matrix_options.parallel = 1;
  matrix_options.brightness = 80;
  matrix_options.pwm_bits = 11;
  matrix_options.pwm_lsb_nanoseconds = 130;
  matrix_options.led_rgb_sequence = "RGB";
  matrix_options.pixel_mapper_config = "";
  matrix_options.row_address_type = 0;
  matrix_options.multiplexing = 1;
  matrix_options.scan_mode = 0;
  matrix_options.disable_hardware_pulsing = false;
  matrix_options.show_refresh_rate = false;
  matrix_options.inverse_colors = false;

  runtime_opt.gpio_slowdown = 2;
  runtime_opt.daemon = 0;
  runtime_opt.drop_privileges = 1;

  // Parse matrix options first
  if (!rgb_matrix::ParseOptionsFromFlags(&argc, &argv, &matrix_options, &runtime_opt)) {
    return usage(argv[0]);
  }

  // Application-specific options (matches Python arguments)
  const char* url = "http://*************/panel-message-3part.json";
  const char* font_file = "../fonts/6x10.bdf";
  Color scroll_color(0, 255, 0); // Green (matches Python default)
  int scroll_speed = 1;
  int target_fps = 10; // Matches Python default after optimization

  int opt;
  while ((opt = getopt(argc, argv, "u:s:f:c:")) != -1) {
    switch (opt) {
      case 'u': url = optarg; break;
      case 's': scroll_speed = atoi(optarg); break;
      case 'f': font_file = optarg; break;
      case 'c':
        if (!parseColor(&scroll_color, optarg)) {
          fprintf(stderr, "Invalid color spec: %s\n", optarg);
          return usage(argv[0]);
        }
        break;
      default: return usage(argv[0]);
    }
  }

  // Initialize curl
  curl_global_init(CURL_GLOBAL_DEFAULT);

  // Initialize display data
  DisplayData display_data;
  memset(&display_data, 0, sizeof(display_data));
  display_data.updated = false;
  pthread_mutex_init(&display_data.mutex, nullptr);

  // Create matrix
  RGBMatrix *matrix = RGBMatrix::CreateFromOptions(matrix_options, runtime_opt);
  if (matrix == nullptr) {
    fprintf(stderr, "Failed to create matrix\n");
    return 1;
  }

  // Load fonts (matches Python font setup)
  Font static_font;
  if (!static_font.LoadFont("../fonts/6x10.bdf")) {
    fprintf(stderr, "Couldn't load static font\n");
    return 1;
  }

  Font scroll_font;
  if (!scroll_font.LoadFont(font_file)) {
    fprintf(stderr, "Couldn't load scroll font '%s'\n", font_file);
    return 1;
  }

  // Set up signal handler
  signal(SIGTERM, InterruptHandler);
  signal(SIGINT, InterruptHandler);

  // Start background data fetcher (matches Python threading)
  pthread_t fetch_thread;
  struct {
    const char* url;
    DisplayData* data;
  } fetch_params = {url, &display_data};

  pthread_create(&fetch_thread, nullptr, DataFetcherThread, &fetch_params);

  // Display loop (matches Python run method)
  FrameCanvas *offscreen = matrix->CreateFrameCanvas();

  // Colors (matches Python color setup)
  Color color_yellow(255, 255, 0);
  Color color_cyan(0, 255, 255);

  // Layout constants (matches Python MID_X1, MID_X2, MID_WIDTH)
  const int MID_X1 = 32;
  const int MID_X2 = 128;
  const int MID_WIDTH = MID_X2 - MID_X1;

  // Scroll state (matches Python scroll_pos1, scroll_pos2)
  int scroll_pos1 = 0;
  int scroll_pos2 = 0;

  // Scroll data (matches Python scroll_data1, scroll_data2)
  ScrollData scroll_data1, scroll_data2;

  // Local copies of text data (matches Python local variables)
  std::string local_line1_left, local_line1_mid, local_line1_right;
  std::string local_line2_left, local_line2_mid, local_line2_right;
  std::string last_text1, last_text2;

  // Frame timing (matches Python frame_time calculation)
  const int frame_delay_usec = 1000000 / target_fps;

  printf("Starting display loop. URL: %s\n", url);
  printf("Press CTRL-C to stop.\n");

  while (!interrupt_received) {
    auto loop_start = std::chrono::steady_clock::now();

    // Copy data from shared structure if updated (matches Python logic)
    if (display_data.updated) {
      pthread_mutex_lock(&display_data.mutex);
      local_line1_left = display_data.line1_left;
      local_line1_mid = display_data.line1_mid;
      local_line1_right = display_data.line1_right;
      local_line2_left = display_data.line2_left;
      local_line2_mid = display_data.line2_mid;
      local_line2_right = display_data.line2_right;
      display_data.updated = false;
      pthread_mutex_unlock(&display_data.mutex);
    }

    offscreen->Clear();

    // Draw static left text (matches Python graphics.DrawText calls)
    DrawText(offscreen, static_font, 0, 14, color_yellow, nullptr, local_line1_left.c_str());
    DrawText(offscreen, static_font, 0, 28, color_yellow, nullptr, local_line2_left.c_str());

    // Draw right text (right-aligned, matches Python logic)
    if (!local_line1_right.empty()) {
      int right_width1 = 0;
      for (char c : local_line1_right) {
        right_width1 += static_font.CharacterWidth(c);
      }
      DrawText(offscreen, static_font, matrix->width() - right_width1, 14, color_cyan, nullptr, local_line1_right.c_str());
    }

    if (!local_line2_right.empty()) {
      int right_width2 = 0;
      for (char c : local_line2_right) {
        right_width2 += static_font.CharacterWidth(c);
      }
      DrawText(offscreen, static_font, matrix->width() - right_width2, 28, color_cyan, nullptr, local_line2_right.c_str());
    }

    // Update scroll data if text changed (matches Python logic)
    if (local_line1_mid != last_text1) {
      scroll_data1 = PrepareScrollData(local_line1_mid, scroll_font, offscreen, MID_WIDTH);
      last_text1 = local_line1_mid;
      scroll_pos1 = 0;
    }

    if (local_line2_mid != last_text2) {
      scroll_data2 = PrepareScrollData(local_line2_mid, scroll_font, offscreen, MID_WIDTH);
      last_text2 = local_line2_mid;
      scroll_pos2 = 0;
    }

    // Draw middle sections (matches Python middle section logic)
    if (scroll_data1.valid) {
      if (scroll_data1.total_width <= MID_WIDTH) {
        // Center the text if it fits
        DrawCenteredText(offscreen, scroll_font, local_line1_mid, MID_X1, 14, MID_WIDTH, scroll_color);
      } else {
        // Scroll the text if it's too long
        DrawScrollingText(offscreen, scroll_font, scroll_data1, scroll_pos1, MID_X1, 14, MID_WIDTH, scroll_color);
        scroll_pos1 = (scroll_pos1 + scroll_speed) % scroll_data1.total_width;
      }
    }

    if (scroll_data2.valid) {
      if (scroll_data2.total_width <= MID_WIDTH) {
        // Center the text if it fits
        DrawCenteredText(offscreen, scroll_font, local_line2_mid, MID_X1, 28, MID_WIDTH, scroll_color);
      } else {
        // Scroll the text if it's too long
        DrawScrollingText(offscreen, scroll_font, scroll_data2, scroll_pos2, MID_X1, 28, MID_WIDTH, scroll_color);
        scroll_pos2 = (scroll_pos2 + scroll_speed) % scroll_data2.total_width;
      }
    }

    // Swap buffers (matches Python SwapOnVSync)
    offscreen = matrix->SwapOnVSync(offscreen);

    // Frame rate control (matches Python frame timing)
    usleep(frame_delay_usec);
  }

  // Cleanup
  pthread_cancel(fetch_thread);
  pthread_join(fetch_thread, nullptr);
  pthread_mutex_destroy(&display_data.mutex);
  curl_global_cleanup();
  delete matrix;

  printf("\nReceived CTRL-C. Exiting.\n");
  return 0;
}
