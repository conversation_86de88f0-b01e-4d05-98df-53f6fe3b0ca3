#!/usr/bin/env python3
from samplebase import SampleBase
from rgbmatrix import graphics
import time


class PreciseScrollRegion(SampleBase):
    def __init__(self, *args, **kwargs):
        super(PreciseScrollRegion, self).__init__(*args, **kwargs)
        self.parser.add_argument("-t", "--text", help="Middle scrolling text", default="Middle text scrolling demo")

    def run(self):
        canvas = self.matrix.CreateFrameCanvas()
        font = graphics.Font()
        font.LoadFont("../../../fonts/7x13.bdf")

        color_left = graphics.Color(255, 255, 0)
        color_mid = graphics.Color(0, 255, 0)
        color_right = graphics.Color(0, 255, 255)

        MID_START_X = 40
        MID_END_X = 120
        MID_WIDTH = MID_END_X - MID_START_X

        scroll_text = self.args.text + "   "
        scroll_x = 0

        # Precompute pixel widths of each character
        char_widths = [graphics.DrawText(canvas, font, 0, 0, color_mid, c) for c in scroll_text]
        total_width = sum(char_widths)

        while True:
            canvas.Clear()

            # Fixed left and right text
            graphics.DrawText(canvas, font, 0, 14, color_left, "LEFT")
            right_text = "RIGHT"
            right_width = graphics.DrawText(canvas, font, 0, 0, color_right, right_text)
            graphics.DrawText(canvas, font, canvas.width - right_width, 14, color_right, right_text)

            # Compute starting position in pixel space
            pixel_x = -scroll_x
            x_draw = MID_START_X

            # Draw visible chars only
            i = 0
            while pixel_x < MID_WIDTH and i < len(scroll_text):
                if pixel_x + char_widths[i] > 0:
                    graphics.DrawText(canvas, font, x_draw + pixel_x, 14, color_mid, scroll_text[i])
                pixel_x += char_widths[i]
                i += 1

            scroll_x += 1
            if scroll_x >= total_width:
                scroll_x = 0

            time.sleep(0.05)
            canvas = self.matrix.SwapOnVSync(canvas)


if __name__ == "__main__":
    app = PreciseScrollRegion()
    if not app.process():
        app.print_help()
