#!/usr/bin/env python3
import time
import requests
from PIL import Image, ImageDraw, ImageFont
from samplebase import SampleBase
from rgbmatrix import graphics
import threading
import queue


class HighPerformanceScrollDisplay(SampleBase):
    def __init__(self, *args, **kwargs):
        super(HighPerformanceScrollDisplay, self).__init__(*args, **kwargs)
        self.parser.add_argument("-u", "--url", required=True, help="JSON URL with left, mid, right for line1 and line2")
        self.parser.add_argument("--color", default="0,255,0", help="Scroll text RGB color, e.g. 255,255,0")
        self.parser.add_argument("--font", default="/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf", help="TTF font path")
        self.parser.add_argument("--scroll-speed", type=float, default=1.0, help="Scroll speed multiplier (default: 1.0)")
        self.parser.add_argument("--fps", type=int, default=30, help="Target FPS for smooth scrolling (default: 30)")
        
        # Performance optimization variables
        self.scroll_buffers = {}
        self.pixel_cache = {}
        self.data_queue = queue.Queue(maxsize=2)
        self.current_data = None
        self.data_thread = None
        self.running = True

    def fetch_remote_data(self):
        """Fetch data in background thread"""
        try:
            response = requests.get(self.args.url, timeout=3)
            response.raise_for_status()
            data = response.json()
            return data.get("line1", {"left": "", "mid": "", "right": ""}), data.get("line2", {"left": "", "mid": "", "right": ""})
        except Exception as e:
            print(f"[WARN] Fetch failed: {e}")
            return ({"left": "", "mid": "", "right": ""}, {"left": "", "mid": "", "right": ""})

    def data_fetcher_thread(self):
        """Background thread for fetching data"""
        while self.running:
            try:
                data = self.fetch_remote_data()
                if not self.data_queue.full():
                    self.data_queue.put(data, block=False)
            except queue.Full:
                pass  # Skip if queue is full
            time.sleep(1.0)  # Fetch every second

    def create_optimized_scroll_buffer(self, text, font, width, height):
        """Create an optimized scroll buffer with pre-calculated pixel data"""
        if not text.strip():
            return None, 0
            
        # Check cache first
        cache_key = f"{text}_{width}_{height}"
        if cache_key in self.scroll_buffers:
            return self.scroll_buffers[cache_key]
        
        # Calculate text dimensions
        dummy_img = Image.new("1", (1, 1))
        dummy_draw = ImageDraw.Draw(dummy_img)
        try:
            text_width, text_height = dummy_draw.textsize(text, font=font)
        except AttributeError:
            # For newer Pillow versions
            bbox = dummy_draw.textbbox((0, 0), text, font=font)
            text_width, text_height = bbox[2] - bbox[0], bbox[3] - bbox[1]

        if text_width <= width:
            return None, text_width  # No scrolling needed
        
        # Add padding for smooth loop
        padded_text = text + "   "
        padded_width = text_width + 30  # Approximate padding width
        
        # Create scroll image
        scroll_img = Image.new("1", (padded_width, height), 0)
        draw = ImageDraw.Draw(scroll_img)
        y_pos = (height - text_height) // 2
        draw.text((0, y_pos), padded_text, font=font, fill=1)
        
        # Pre-calculate pixel data for faster access
        pixel_data = []
        for x in range(padded_width):
            column = []
            for y in range(height):
                column.append(scroll_img.getpixel((x, y)))
            pixel_data.append(column)
        
        buffer_data = {
            'pixel_data': pixel_data,
            'width': padded_width,
            'height': height,
            'text_width': text_width
        }
        
        # Cache the buffer (limit cache size)
        if len(self.scroll_buffers) > 10:
            # Remove oldest entry
            oldest_key = next(iter(self.scroll_buffers))
            del self.scroll_buffers[oldest_key]
        
        self.scroll_buffers[cache_key] = (buffer_data, text_width)
        return buffer_data, text_width

    def draw_optimized_scroll(self, canvas, buffer_data, scroll_pos, x_start, y_start, width, height, color):
        """Optimized scroll drawing using pre-calculated pixel data"""
        if not buffer_data:
            return
            
        pixel_data = buffer_data['pixel_data']
        buffer_width = buffer_data['width']
        
        # Calculate scroll offset with smooth wrapping
        scroll_offset = int(scroll_pos) % buffer_width
        
        # Draw pixels efficiently
        for x in range(width):
            src_x = (scroll_offset + x) % buffer_width
            if src_x < len(pixel_data):
                column = pixel_data[src_x]
                for y in range(min(height, len(column))):
                    if column[y]:
                        canvas.SetPixel(x_start + x, y_start + y, *color)

    def draw_centered_text(self, canvas, text, font, x_start, y_start, width, height, color):
        """Draw centered text efficiently"""
        if not text.strip():
            return
            
        # Use cache for centered text
        cache_key = f"center_{text}_{width}_{height}"
        if cache_key in self.pixel_cache:
            pixel_data = self.pixel_cache[cache_key]
        else:
            # Create centered text image
            img = Image.new("1", (width, height), 0)
            draw = ImageDraw.Draw(img)
            
            try:
                tw, th = draw.textsize(text, font=font)
            except AttributeError:
                bbox = draw.textbbox((0, 0), text, font=font)
                tw, th = bbox[2] - bbox[0], bbox[3] - bbox[1]
            
            x_pos = max(0, (width - tw) // 2)
            y_pos = max(0, (height - th) // 2)
            draw.text((x_pos, y_pos), text, font=font, fill=1)
            
            # Cache pixel data
            pixel_data = []
            for y in range(height):
                row = []
                for x in range(width):
                    row.append(img.getpixel((x, y)))
                pixel_data.append(row)
            
            if len(self.pixel_cache) > 20:
                # Clear cache if too large
                self.pixel_cache.clear()
            self.pixel_cache[cache_key] = pixel_data
        
        # Draw cached pixels
        for y in range(height):
            for x in range(width):
                if pixel_data[y][x]:
                    canvas.SetPixel(x_start + x, y_start + y, *color)

    def run(self):
        canvas = self.matrix.CreateFrameCanvas()
        HEIGHT = canvas.height
        WIDTH = canvas.width

        # Zone configuration
        MID_X1 = 32
        MID_X2 = 128
        MID_WIDTH = MID_X2 - MID_X1

        # Font setup
        static_font = graphics.Font()
        static_font.LoadFont("../../../fonts/6x10.bdf")

        try:
            pil_font = ImageFont.truetype(self.args.font, 10)
        except:
            pil_font = ImageFont.load_default()

        # Performance settings
        scroll_color = tuple(map(int, self.args.color.split(",")))
        target_fps = self.args.fps
        frame_time = 1.0 / target_fps
        scroll_speed = self.args.scroll_speed
        
        # Scroll state
        scroll_pos1 = 0.0
        scroll_pos2 = 0.0
        scroll_buffer1 = None
        scroll_buffer2 = None
        text_width1 = 0
        text_width2 = 0
        
        last_text1 = ""
        last_text2 = ""
        
        # Start background data fetching
        self.data_thread = threading.Thread(target=self.data_fetcher_thread, daemon=True)
        self.data_thread.start()
        
        # Initial data fetch
        self.current_data = self.fetch_remote_data()
        
        frame_start_time = time.time()
        
        try:
            while True:
                loop_start = time.time()
                
                # Get latest data if available
                try:
                    while not self.data_queue.empty():
                        self.current_data = self.data_queue.get_nowait()
                except queue.Empty:
                    pass
                
                if self.current_data:
                    line1, line2 = self.current_data
                else:
                    line1 = line2 = {"left": "", "mid": "", "right": ""}
                
                canvas.Clear()

                # Draw static left/right text
                graphics.DrawText(canvas, static_font, 0, 14, graphics.Color(255, 255, 0), line1["left"])
                rtext1 = line1["right"]
                if rtext1:
                    rw1 = graphics.DrawText(canvas, static_font, 0, 0, graphics.Color(0, 255, 255), rtext1)
                    graphics.DrawText(canvas, static_font, WIDTH - rw1, 14, graphics.Color(0, 255, 255), rtext1)

                graphics.DrawText(canvas, static_font, 0, 28, graphics.Color(255, 255, 0), line2["left"])
                rtext2 = line2["right"]
                if rtext2:
                    rw2 = graphics.DrawText(canvas, static_font, 0, 0, graphics.Color(0, 255, 255), rtext2)
                    graphics.DrawText(canvas, static_font, WIDTH - rw2, 28, graphics.Color(0, 255, 255), rtext2)

                # Update scroll buffers if text changed
                if line1["mid"] != last_text1:
                    scroll_buffer1, text_width1 = self.create_optimized_scroll_buffer(
                        line1["mid"], pil_font, MID_WIDTH, 16)
                    last_text1 = line1["mid"]
                    scroll_pos1 = 0.0

                if line2["mid"] != last_text2:
                    scroll_buffer2, text_width2 = self.create_optimized_scroll_buffer(
                        line2["mid"], pil_font, MID_WIDTH, 16)
                    last_text2 = line2["mid"]
                    scroll_pos2 = 0.0

                # Draw middle sections
                if text_width1 <= MID_WIDTH:
                    self.draw_centered_text(canvas, line1["mid"], pil_font, MID_X1, 0, MID_WIDTH, 16, scroll_color)
                else:
                    self.draw_optimized_scroll(canvas, scroll_buffer1, scroll_pos1, MID_X1, 0, MID_WIDTH, 16, scroll_color)
                    scroll_pos1 += scroll_speed

                if text_width2 <= MID_WIDTH:
                    self.draw_centered_text(canvas, line2["mid"], pil_font, MID_X1, 16, MID_WIDTH, 16, scroll_color)
                else:
                    self.draw_optimized_scroll(canvas, scroll_buffer2, scroll_pos2, MID_X1, 16, MID_WIDTH, 16, scroll_color)
                    scroll_pos2 += scroll_speed

                canvas = self.matrix.SwapOnVSync(canvas)
                
                # Frame rate control
                elapsed = time.time() - loop_start
                sleep_time = max(0, frame_time - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    
        except KeyboardInterrupt:
            self.running = False
            if self.data_thread:
                self.data_thread.join(timeout=1.0)


if __name__ == "__main__":
    app = HighPerformanceScrollDisplay()
    if not app.process():
        app.print_help()
