#!/bin/bash
# Simple optimization for showremotetext_3part_3.py
# No compilation needed - just system-level optimizations

set -e

echo "🚀 Simple Performance Optimization for showremotetext_3part_3.py"
echo "No compilation required - just system optimizations!"

# Check if we're in the right directory
if [ ! -f "showremotetext_3part_3.py" ]; then
    echo "❌ Error: showremotetext_3part_3.py not found!"
    echo "Please run this script from the bindings/python/samples directory"
    exit 1
fi

echo ""
echo "🔧 Applying system-level optimizations..."

# 1. Install PyPy3 for much faster Python execution
echo "📦 Installing PyPy3 (2-5x faster Python)..."
sudo apt-get update
sudo apt-get install -y pypy3 pypy3-dev

# 2. Try to install requests for PyPy
echo "📦 Installing requests for PyPy..."
if command -v pypy3 &> /dev/null; then
    # Try to install requests for PyPy
    if ! pypy3 -c "import requests" 2>/dev/null; then
        # Install pip for PyPy if needed
        if ! command -v pypy3-pip &> /dev/null; then
            sudo apt-get install -y pypy3-pip || {
                echo "Installing pip for PyPy manually..."
                wget -q https://bootstrap.pypa.io/get-pip.py
                pypy3 get-pip.py --user
                rm get-pip.py
            }
        fi
        
        # Install requests
        pypy3 -m pip install --user requests || {
            echo "⚠️  Could not install requests for PyPy, will use regular Python"
        }
    fi
fi

# 3. Create optimized launcher script
echo "📝 Creating optimized launcher script..."
cat > showremotetext-optimized.py << 'EOF'
#!/usr/bin/env python3
"""
Optimized launcher for showremotetext_3part_3.py
Tries PyPy first for better performance, falls back to regular Python
"""

import sys
import os
import subprocess

def try_pypy():
    """Try to run with PyPy for better performance"""
    try:
        # Check if PyPy is available and has required modules
        result = subprocess.run(['pypy3', '-c', 'import requests, rgbmatrix'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("🚀 Using PyPy3 for 2-5x better performance!")
            # Run with PyPy
            cmd = ['pypy3', 'showremotetext_3part_3.py'] + sys.argv[1:]
            os.execvp('pypy3', cmd)
        else:
            return False
    except (FileNotFoundError, subprocess.SubprocessError):
        return False

def run_regular_python():
    """Run with regular Python with optimizations"""
    print("🐍 Using regular Python with optimizations...")
    
    # Set Python optimizations
    os.environ['PYTHONOPTIMIZE'] = '2'  # Enable optimizations
    os.environ['PYTHONDONTWRITEBYTECODE'] = '1'  # Don't write .pyc files
    
    # Import and run the main script
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    try:
        import showremotetext_3part_3
        app = showremotetext_3part_3.HighPerformanceScrollDisplay()
        if not app.process():
            app.print_help()
    except ImportError as e:
        print(f"❌ Error importing module: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Try PyPy first, fall back to regular Python
    if not try_pypy():
        print("⚠️  PyPy not available or missing modules, using regular Python")
        run_regular_python()
EOF

chmod +x showremotetext-optimized.py

# 4. Create performance tuning script
echo "📝 Creating performance tuning script..."
cat > tune-performance.sh << 'EOF'
#!/bin/bash
# System-level performance tuning for Raspberry Pi

echo "🔧 Applying Raspberry Pi performance tuning..."

# CPU Governor - set to performance mode
echo "Setting CPU governor to performance..."
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Disable swap to reduce SD card wear and improve performance
echo "Disabling swap..."
sudo swapoff -a

# Set nice priority for better scheduling
echo "Setting process priority..."
echo "Use: sudo nice -n -10 python3 showremotetext-optimized.py [options]"

# Memory optimization
echo "Optimizing memory..."
echo 1 | sudo tee /proc/sys/vm/drop_caches

echo "✅ Performance tuning applied!"
echo ""
echo "🚀 To run with maximum performance:"
echo "sudo nice -n -10 python3 showremotetext-optimized.py \\"
echo "  --led-row-addr-type=0 \\"
echo "  --led-multiplexing=1 \\"
echo "  --led-slowdown-gpio=2 \\"
echo "  --led-chain=5 \\"
echo "  --led-brightness=80 \\"
echo "  -u http://*************/panel-message-3part.json"
EOF

chmod +x tune-performance.sh

# 5. Test PyPy availability
echo ""
echo "🧪 Testing PyPy availability..."
if command -v pypy3 &> /dev/null; then
    echo "✅ PyPy3 is available!"
    
    # Test basic functionality
    if pypy3 -c "import sys; print(f'PyPy version: {sys.version}')" 2>/dev/null; then
        echo "✅ PyPy3 is working correctly"
        
        # Test requests module
        if pypy3 -c "import requests" 2>/dev/null; then
            echo "✅ requests module available in PyPy"
        else
            echo "⚠️  requests module not available in PyPy"
        fi
        
        # Test rgbmatrix module
        if pypy3 -c "import rgbmatrix" 2>/dev/null; then
            echo "✅ rgbmatrix module available in PyPy"
            PYPY_COMPATIBLE=true
        else
            echo "⚠️  rgbmatrix module not available in PyPy (this is normal)"
            PYPY_COMPATIBLE=false
        fi
    fi
else
    echo "❌ PyPy3 installation failed"
    PYPY_COMPATIBLE=false
fi

echo ""
echo "✅ Optimization complete!"
echo ""
echo "🚀 Usage options:"
echo ""

if [ "$PYPY_COMPATIBLE" = true ]; then
    echo "🥇 Best performance (PyPy + tuning):"
    echo "  sudo ./tune-performance.sh"
    echo "  sudo nice -n -10 pypy3 showremotetext_3part_3.py [options]"
    echo ""
fi

echo "🥈 Good performance (optimized launcher):"
echo "  sudo python3 showremotetext-optimized.py \\"
echo "    --led-row-addr-type=0 \\"
echo "    --led-multiplexing=1 \\"
echo "    --led-slowdown-gpio=2 \\"
echo "    --led-chain=5 \\"
echo "    --led-brightness=80 \\"
echo "    -u http://*************/panel-message-3part.json"
echo ""

echo "🥉 Standard performance (with system tuning):"
echo "  sudo ./tune-performance.sh"
echo "  sudo nice -n -10 python3 showremotetext_3part_3.py [options]"
echo ""

echo "📊 Expected improvements:"
echo "  - PyPy: 2-5x faster execution"
echo "  - System tuning: 10-20% better performance"
echo "  - Process priority: Reduced latency"
echo "  - Memory optimization: Better responsiveness"
echo ""

echo "🎯 Recommended: Try the optimized launcher first!"
