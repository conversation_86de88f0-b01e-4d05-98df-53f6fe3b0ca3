#!/usr/bin/env python3
from samplebase import SampleBase
from rgbmatrix import graphics
import time
import requests


class RemoteTextDisplay(SampleBase):
    def __init__(self, *args, **kwargs):
        super(RemoteTextDisplay, self).__init__(*args, **kwargs)
        self.parser.add_argument("-u", "--url", help="Remote JSON endpoint", required=True)
        self.parser.add_argument("-i", "--interval", type=int, default=5, help="Refresh interval in seconds")

    def fetch_remote_data(self, url):
        try:
            response = requests.get(url, timeout=3)
            response.raise_for_status()
            data = response.json()
            return (
                data.get("line1", {"left": "", "mid": "", "right": ""}),
                data.get("line2", {"left": "", "mid": "", "right": ""})
            )
        except Exception as e:
            print(f"[WARN] Failed to fetch remote data: {e}")
            return {"left": "", "mid": "", "right": ""}, {"left": "", "mid": "", "right": ""}

    def run(self):
        canvas = self.matrix.CreateFrameCanvas()
        font = graphics.Font()
        font.LoadFont("../../../fonts/7x13.bdf")

        # Colors
        color_left = graphics.Color(255, 255, 0)
        color_mid = graphics.Color(0, 255, 0)
        color_right = graphics.Color(0, 255, 255)

        # Scroll region
        MID_START_X = 40
        MID_END_X = 120
        MID_WIDTH = MID_END_X - MID_START_X

        # Track scroll state
        scroll_pos1 = 0
        scroll_pos2 = 0
        char_widths1 = []
        char_widths2 = []
        total_width1 = 0
        total_width2 = 0

        last_data1 = {"left": "", "mid": "", "right": ""}
        last_data2 = {"left": "", "mid": "", "right": ""}

        while True:
            line1, line2 = self.fetch_remote_data(self.args.url)

            # Prepare scroll info if mid changed
            if line1["mid"] != last_data1["mid"]:
                scroll_text1 = line1["mid"] + "   "
                char_widths1 = [graphics.DrawText(canvas, font, 0, 0, color_mid, c) for c in scroll_text1]
                total_width1 = sum(char_widths1)
                scroll_pos1 = 0
                last_data1["mid"] = line1["mid"]

            if line2["mid"] != last_data2["mid"]:
                scroll_text2 = line2["mid"] + "   "
                char_widths2 = [graphics.DrawText(canvas, font, 0, 0, color_mid, c) for c in scroll_text2]
                total_width2 = sum(char_widths2)
                scroll_pos2 = 0
                last_data2["mid"] = line2["mid"]

            canvas.Clear()

            # Line 1
            graphics.DrawText(canvas, font, 0, 14, color_left, line1["left"])
            right1 = line1["right"]
            right1_width = graphics.DrawText(canvas, font, 0, 0, color_right, right1)
            graphics.DrawText(canvas, font, canvas.width - right1_width, 14, color_right, right1)

            self.draw_mid(canvas, font, color_mid, scroll_text1, scroll_pos1, char_widths1, total_width1, MID_START_X, MID_END_X, y=14)

            # Line 2
            graphics.DrawText(canvas, font, 0, 28, color_left, line2["left"])
            right2 = line2["right"]
            right2_width = graphics.DrawText(canvas, font, 0, 0, color_right, right2)
            graphics.DrawText(canvas, font, canvas.width - right2_width, 28, color_right, right2)

            self.draw_mid(canvas, font, color_mid, scroll_text2, scroll_pos2, char_widths2, total_width2, MID_START_X, MID_END_X, y=28)

            # Advance scroll
            scroll_pos1 = (scroll_pos1 + 1) % (total_width1 if total_width1 > 0 else 1)
            scroll_pos2 = (scroll_pos2 + 1) % (total_width2 if total_width2 > 0 else 1)

            canvas = self.matrix.SwapOnVSync(canvas)
            time.sleep(0.08)

    def draw_mid(self, canvas, font, color, text, scroll_pos, char_widths, total_width, x_start, x_end, y):
        x = -scroll_pos
        i = 0
        while x < (x_end - x_start) and i < len(text):
            if x + char_widths[i] > 0:
                graphics.DrawText(canvas, font, x_start + x, y, color, text[i])
            x += char_widths[i]
            i += 1


# Entry Point
if __name__ == "__main__":
    display = RemoteTextDisplay()
    if not display.process():
        display.print_help()
