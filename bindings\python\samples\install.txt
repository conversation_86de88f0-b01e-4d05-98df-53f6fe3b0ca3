sudo apt update
sudo apt upgrade
sudo apt install git python3-dev cython3 python3-pil -y

git clone https://github.com/hzeller/rpi-rgb-led-matrix.git
#
cd rpi-rgb-led-matrix
make -C examples-api-use
#
cd bindings/python
make build-python 
sudo make install-python 


# run lsmod and see the snd_bcm2835
cat <<EOF | sudo tee /etc/modprobe.d/blacklist-rgb-matrix.conf
blacklist snd_bcm2835
EOF

sudo update-initramfs -u
sudo apt-get remove bluez bluez-firmware pi-bluetooth triggerhappy pigpio -y

#add isolcpus=3 at the end of /boot/firmware/cmdline.txt and reboot
sudo reboot

sudo examples-api-use/demo -D0 --led-row-addr-type=0 --led-multiplexing=1 --led-slowdown-gpio=6 --led-chain=5 --led-brightness=50

RPi Zero
sudo python showremotetext_3part_2.py --led-row-addr-type=0 --led-multiplexing=1 --led-slowdown-gpio=2 --led-chain=5 --led-brightness=80 -u http://*************/panel-message-3part.json

sudo python scrolltext_2.py --led-row-addr-type=0 --led-multiplexing=1 --led-slowdown-gpio=2 --led-chain=5 --led-brightness=80