#!/usr/bin/env python3
from samplebase import SampleBase
from rgbmatrix import graphics
import time
import requests


class RemoteTextDisplay(SampleBase):
    def __init__(self, *args, **kwargs):
        super(RemoteTextDisplay, self).__init__(*args, **kwargs)
        self.parser.add_argument(
            "-u", "--url", help="Remote JSON endpoint", required=True
        )
        self.parser.add_argument(
            "-i", "--interval", type=int, default=5,
            help="Refresh interval in seconds (default: 5s)"
        )

    def fetch_remote_data(self, url):
        try:
            response = requests.get(url, timeout=3)
            response.raise_for_status()
            data = response.json()
            return data.get("line1", ""), data.get("line2", "")
        except Exception as e:
            print(f"[WARN] Failed to fetch remote data: {e}")
            return "", ""

    def run(self):
        offscreen_canvas = self.matrix.CreateFrameCanvas()
        font = graphics.Font()
        font.LoadFont("../../../fonts/7x14.bdf")  # 6x13

        color1 = graphics.Color(255, 255, 0)
        color2 = graphics.Color(0, 255, 255)

        last_line1, last_line2 = "", ""

        while True:
            line1, line2 = self.fetch_remote_data(self.args.url)

            # Update only if changed
            if line1 != last_line1 or line2 != last_line2:
                print(f"[INFO] Updating display:\n  Line 1: {line1}\n  Line 2: {line2}")
                offscreen_canvas.Clear()
                graphics.DrawText(offscreen_canvas, font, 2, 12, color1, line1)
                graphics.DrawText(offscreen_canvas, font, 2, 28, color2, line2)
                offscreen_canvas = self.matrix.SwapOnVSync(offscreen_canvas)
                last_line1, last_line2 = line1, line2
            else:
                print("[DEBUG] No change detected.")

            time.sleep(self.args.interval)


# --- Entry Point ---
if __name__ == "__main__":
    display = RemoteTextDisplay()
    if not display.process():
        display.print_help()
