#!/bin/bash
# Compile Python showremotetext_3part_3.py to native binary using Nuitka
# This provides 2-3x performance improvement while keeping exact functionality

set -e

echo "🚀 Compiling Python showremotetext_3part_3.py to native binary..."

# Check if we're in the right directory
if [ ! -f "showremotetext_3part_3.py" ]; then
    echo "❌ Error: showremotetext_3part_3.py not found!"
    echo "Please run this script from the bindings/python/samples directory"
    exit 1
fi

# Install Nuitka if not present
echo "📦 Checking for Nuitka..."
if ! python3 -c "import nuitka" 2>/dev/null; then
    echo "Installing Nuitka compiler..."

    # Try system package first
    if sudo apt-get install -y python3-nuitka 2>/dev/null; then
        echo "✅ Installed Nuitka via apt"
    else
        echo "📦 System package not available, creating virtual environment..."

        # Install python3-venv if not present
        sudo apt-get install -y python3-venv python3-full

        # Create virtual environment
        python3 -m venv nuitka-env
        source nuitka-env/bin/activate

        # Install in virtual environment
        pip install nuitka

        echo "✅ Installed Nuitka in virtual environment"
        echo "ℹ️  Virtual environment created at: $(pwd)/nuitka-env"
    fi
fi

# Install additional dependencies for compilation
echo "📦 Installing compilation dependencies..."
sudo apt-get update
sudo apt-get install -y python3-dev build-essential ccache

# Create output directory
mkdir -p compiled

# Determine which Python/Nuitka to use
PYTHON_CMD="python3"
NUITKA_CMD="python3 -m nuitka"

# Check if we need to use virtual environment
if [ -d "nuitka-env" ]; then
    echo "🔧 Using virtual environment for compilation..."
    source nuitka-env/bin/activate
    PYTHON_CMD="python"
    NUITKA_CMD="python -m nuitka"
fi

# Compile the Python script to native binary
echo "⚙️ Compiling showremotetext_3part_3.py..."
$NUITKA_CMD \
    --standalone \
    --onefile \
    --output-dir=compiled \
    --output-filename=showremotetext-3part-compiled \
    --remove-output \
    --assume-yes-for-downloads \
    --follow-imports \
    --include-module=requests \
    --include-module=rgbmatrix \
    --include-module=samplebase \
    --include-module=queue \
    --include-module=threading \
    --include-module=time \
    --include-module=json \
    --python-flag=no_site \
    --python-flag=no_warnings \
    showremotetext_3part_3.py

# Check if compilation was successful
if [ -f "compiled/showremotetext-3part-compiled" ]; then
    echo ""
    echo "✅ Compilation successful!"
    echo ""
    echo "📁 Compiled binary location: compiled/showremotetext-3part-compiled"
    echo "📊 File size: $(du -h compiled/showremotetext-3part-compiled | cut -f1)"
    echo ""
    echo "🚀 Usage examples:"
    echo "  # Basic usage with defaults:"
    echo "  sudo ./compiled/showremotetext-3part-compiled"
    echo ""
    echo "  # With custom parameters:"
    echo "  sudo ./compiled/showremotetext-3part-compiled \\"
    echo "    --led-row-addr-type=0 \\"
    echo "    --led-multiplexing=1 \\"
    echo "    --led-slowdown-gpio=2 \\"
    echo "    --led-chain=5 \\"
    echo "    --led-brightness=80 \\"
    echo "    -u http://*************/panel-message-3part.json"
    echo ""
    echo "⚡ Performance benefits:"
    echo "  - 2-3x faster than regular Python"
    echo "  - Single executable file"
    echo "  - No Python interpreter needed at runtime"
    echo "  - Same exact functionality as Python version"
    echo "  - All display issues already solved"
    echo ""
    echo "🔧 The compiled binary includes all dependencies and can be run directly!"
    
    # Make executable
    chmod +x compiled/showremotetext-3part-compiled
    
else
    echo "❌ Compilation failed!"
    echo "Check the error messages above for details."
    exit 1
fi
