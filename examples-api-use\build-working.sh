#!/bin/bash
# Build script for showremotetext-3part-working.cc
# C++ version based on working Python showremotetext_3part_3.py

set -e

echo "🚀 Building C++ version based on working Python code..."

# Check if we're in the right directory
if [ ! -f "showremotetext-3part-working.cc" ]; then
    echo "❌ Error: showremotetext-3part-working.cc not found!"
    echo "Please run this script from the examples-api-use directory"
    exit 1
fi

# Install dependencies if needed
echo "📦 Checking dependencies..."
if ! pkg-config --exists libcurl; then
    echo "Installing libcurl development package..."
    sudo apt-get update
    sudo apt-get install -y libcurl4-openssl-dev
fi

if ! pkg-config --exists json-c; then
    echo "Installing json-c development package..."
    sudo apt-get install -y libjson-c-dev
fi

# Build the main library first
echo "🔧 Building RGB matrix library..."
make -C .. -j$(nproc)

# Build our program
echo "🔧 Building showremotetext-3part-working..."
make -f Makefile.showremotetext-working clean
make -f Makefile.showremotetext-working -j$(nproc)

if [ -f "showremotetext-3part-working" ]; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    echo "📊 This C++ version replicates the exact functionality of your working Python code:"
    echo "  ✅ Same 3-part layout (left/center/right on 2 lines)"
    echo "  ✅ Same scrolling logic (character-by-character with pre-calculated widths)"
    echo "  ✅ Same background data fetching (2-second intervals)"
    echo "  ✅ Same JSON parsing and error handling"
    echo "  ✅ Same font usage and text positioning"
    echo "  ✅ Same frame rate control (10 FPS default)"
    echo ""
    echo "🚀 Usage examples:"
    echo ""
    echo "  # Basic usage with defaults (matches Python defaults):"
    echo "  sudo ./showremotetext-3part-working"
    echo ""
    echo "  # With custom parameters:"
    echo "  sudo ./showremotetext-3part-working \\"
    echo "    -u http://your-server/data.json \\"
    echo "    -s 2 \\"
    echo "    -c 255,0,0 \\"
    echo "    -f ../fonts/7x13.bdf"
    echo ""
    echo "  # Full parameter example for RPi Zero (matches your Python setup):"
    echo "  sudo ./showremotetext-3part-working \\"
    echo "    --led-row-addr-type=0 \\"
    echo "    --led-multiplexing=1 \\"
    echo "    --led-slowdown-gpio=2 \\"
    echo "    --led-chain=5 \\"
    echo "    --led-brightness=80 \\"
    echo "    -u http://*************/panel-message-3part.json"
    echo ""
    echo "⚡ Performance benefits over Python:"
    echo "  🚀 24x faster execution (as per official docs)"
    echo "  💾 Much lower memory usage"
    echo "  ⚡ Higher possible frame rates"
    echo "  🔧 Better suited for Raspberry Pi Zero"
    echo ""
    echo "🎯 This version should display exactly the same as your working Python version!"
    echo ""
else
    echo "❌ Build failed!"
    echo "Check the error messages above for details."
    exit 1
fi
